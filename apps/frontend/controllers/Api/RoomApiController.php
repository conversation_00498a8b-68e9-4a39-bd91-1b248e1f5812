<?php

namespace Modules\Frontend\Controllers\Api;

use Mo<PERSON>les\App\Models\HostelModel;
use Modules\App\Models\HostelRoomModel;
use Modules\App\Models\UserModel;

class RoomApiController extends ApiControllerBase
{
  const ACTION_CREATE = 'create';
  const ACTION_UPDATE = 'update';

  public function onConstruct()
  {
    parent::onConstruct();
  }
  
  /**
   * Lấy danh sách loại phòng với filters và phân trang
   * GET /api/hostel/room/list
   */
  public function listAction()
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    // Debug payload
    if (is_array($payload) && isset($payload['status']) && $payload['status'] === false) {
      return $this->sendErrorResponse('JWT Error: ' . $payload['message'], $payload, $this->statusCode::BAD_UNAUTHORIZED);
    }

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', ['payload' => $payload], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Người dùng không tồn tại', ['userId' => $payload->userId], $this->statusCode::BAD_UNAUTHORIZED);
    }

    if (empty($user->host)) {
      return $this->sendErrorResponse('Bạn chưa đăng ký làm chủ trọ', ['user_id' => $user->id, 'host' => $user->host], $this->statusCode::FORBIDDEN);
    }

    // Lấy parameters từ request
    $page = $this->request->getQuery('page', 'int', 1);
    $perPage = $this->request->getQuery('per_page', 'int', 10);
    $hostelId = $this->request->getQuery('hostel_id', 'int', 0);
    $status = $this->request->getQuery('status', 'string', '');
    $sort = $this->request->getQuery('sort', 'string', 'created');
    $sortType = $this->request->getQuery('sort_type', 'string', 'DESC');

    // Build conditions
    $conditions = ['host_id = :host_id:'];
    $bind = ['host_id' => $user->host->id];

    if (!empty($hostelId)) {
      $conditions[] = 'id = :hostel_id:';
      $bind['hostel_id'] = $hostelId;
    }

    // Lấy hostels của user
    $hostels = $user->host->getHostels([
      'conditions' => implode(' AND ', $conditions),
      'bind' => $bind
    ]);

    if ($hostels->count() == 0) {
      return $this->sendSuccessResponse('Không có trọ nào', []);
    }

    // Lấy tất cả rooms từ các hostels
    $allRooms = [];
    foreach ($hostels as $hostel) {
      $roomConditions = ['hostel_id = :hostel_id:'];
      $roomBind = ['hostel_id' => $hostel->id];

      if (!empty($status)) {
        $roomConditions[] = 'status = :status:';
        $roomBind['status'] = $status;
      }

      $rooms = HostelRoomModel::find([
        'conditions' => implode(' AND ', $roomConditions),
        'bind' => $roomBind,
        'order' => $sort . ' ' . $sortType
      ]);

      foreach ($rooms as $room) {
        $allRooms[] = $this->_formatRoomData($room);
      }
    }

    $pagination = $this->paginateArrayData($allRooms, $perPage, $page);

    return $this->sendSuccessResponse('OK', [
      'rooms' => $pagination['items'],
      'pagination' => [
        'page'            => $page ?? 1,
        'per_page'        => $perPage > $pagination['pagination']['total_items'] ? $pagination['pagination']['total_items'] : $perPage,
        'total_items'     => $pagination['pagination']['total_items'],
        'total_page'      => $pagination['pagination']['total_pages'] ?? 1,
      ]
    ]);
  }

  /**
   * Tạo mới loại phòng
   * POST /api/hostel/room/add
   */
  public function addAction()
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    $request = $this->request->getPost();
    $files = $this->request->hasFiles();

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Không tìm thấy thông tin người dùng', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    if (empty($user->host)) {
      return $this->sendErrorResponse('Bạn chưa đăng ký làm chủ trọ', [], $this->statusCode::FORBIDDEN);
    }

    // Xử lý và validate hình ảnh trước khi validate input
    $uploadedFiles = [];
    $uploadedPaths = [];

    if ($files) {
      $uploadedFiles = $this->request->getUploadedFiles();

      // Validate images sử dụng method từ ApiControllerBase
      $imageValidation = $this->validateImages($uploadedFiles);

      if (!empty($imageValidation['invalid'])) {
        $errors = [];
        foreach ($imageValidation['invalid'] as $invalid) {
          $errors[] = $invalid['name'] . ': ' . $invalid['error'];
        }
        return $this->sendErrorResponse('Có lỗi khi tải lên hình ảnh', $errors, $this->statusCode::BAD_REQUEST);
      }

      // Upload images sử dụng method từ ApiControllerBase
      $uploadedPaths = $this->_uploadImage($imageValidation['valid'], self::ACTION_CREATE);

      if (empty($uploadedPaths)) {
        return $this->sendErrorResponse('Không thể upload hình ảnh', [], $this->statusCode::BAD_REQUEST);
      }
    }

    // Validate input
    $validation = $this->_validateRoomInput($request, $user, null, $uploadedPaths);
    if (!$validation['success']) {
      return $this->sendErrorResponse($validation['message'], $validation['errors'] ?? [], $this->statusCode::BAD_REQUEST);
    }

    $hostel = $validation['hostel'];

    // Chuẩn bị dữ liệu cho room
    $roomData = [
      'hostel_id' => $hostel->id,
      'title' => $request['title'],
      'price' => $request['price'],
      'area' => $request['area'],
      'quantity' => $request['quantity'],
      'maximum' => $request['maximum'],
      'gender' => $request['gender'],
      'content' => $request['content'],
      'amenities' => $request['amenities'] ?? [],
      'creator_id' => $user->id,
      'files' => $uploadedPaths
    ];

    // Tạo room mới với images đã được processed
    $result = $this->_createRoomWithProcessedImages($roomData, $uploadedPaths);

    if (!$result['success']) {
      return $this->sendErrorResponse($result['message'], $result['errors'] ?? [], $this->statusCode::BAD_REQUEST);
    }

    $room = $result['hostelRoom'];
    $responseData = $this->_formatRoomData($room);

    return $this->sendSuccessResponse('Tạo loại phòng thành công', $responseData);
  }

  /**
   * Cập nhật loại phòng
   * POST /api/hostel/room/{id}/update
   */
  public function updateAction($id)
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    $request  = $this->request->getPost();
    $files    = $this->request->hasFiles();

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Không tìm thấy thông tin người dùng', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    if (empty($user->host)) {
      return $this->sendErrorResponse('Bạn chưa đăng ký làm chủ trọ', [], $this->statusCode::FORBIDDEN);
    }

    // Tìm room
    $room = HostelRoomModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $id]
    ]);

    if (empty($room)) {
      return $this->sendErrorResponse('Không tìm thấy loại phòng', [], $this->statusCode::NOT_FOUND);
    }

    // Kiểm tra quyền sở hữu
    if ($room->hostel->host_id != $user->host->id) {
      return $this->sendErrorResponse('Không có quyền truy cập', [], $this->statusCode::FORBIDDEN);
    }

    // Xử lý và validate hình ảnh nếu có upload mới
    $uploadedFiles = [];
    $uploadedPaths = [];
    $originFiles = [];

    if ($files) {
      $uploadedFiles = $this->request->getUploadedFiles();

      // Validate images sử dụng method từ ApiControllerBase
      $imageValidation = $this->validateImages($uploadedFiles);

      if (!empty($imageValidation['invalid'])) {
        $errors = [];
        foreach ($imageValidation['invalid'] as $invalid) {
          $errors[] = $invalid['name'] . ': ' . $invalid['error'];
        }
        return $this->sendErrorResponse('Có lỗi khi tải lên hình ảnh', $errors, $this->statusCode::BAD_REQUEST);
      }

      // Upload images sử dụng method từ ApiControllerBase
      $uploadedPaths = $this->_uploadImage($imageValidation['valid'], self::ACTION_UPDATE);

      if (empty($uploadedPaths)) {
        return $this->sendErrorResponse('Không thể upload hình ảnh', [], $this->statusCode::BAD_REQUEST);
      }
    } else {
      // Giữ lại ảnh cũ nếu không upload ảnh mới
      $imgsData = json_decode($room->imgs, true);
      if (!empty($imgsData)) {
        foreach ($imgsData as $img) {
          $originFiles[] = $img['src'];
        }
      }
    }

    // Validate input
    $validation = $this->_validateRoomInput($request, $user, $room, $uploadedPaths ?: $originFiles);
    if (!$validation['success']) {
      return $this->sendErrorResponse($validation['message'], $validation['errors'] ?? [], $this->statusCode::BAD_REQUEST);
    }

    // Chuẩn bị dữ liệu cập nhật
    $roomData = [
      'hostel_id' => $room->hostel_id, // Cần thiết cho validation
      'title' => $request['title'],
      'price' => $request['price'],
      'area' => $request['area'],
      'quantity' => $request['quantity'],
      'maximum' => $request['maximum'],
      'gender' => $request['gender'],
      'content' => $request['content'],
      'amenities' => $request['amenities'] ?? [],
      'editor_id' => $user->id,
      'files' => $uploadedPaths
    ];

    // Cập nhật room với images đã được processed
    $result = $this->_updateRoomWithProcessedImages($room->id, $roomData, $uploadedPaths, $originFiles);

    if (!$result['success']) {
      return $this->sendErrorResponse($result['message'], $result['errors'] ?? [], $this->statusCode::BAD_REQUEST);
    }

    // Reload room data sau khi update
    $room = $result['hostelRoom'];

    $responseData = $this->_formatRoomData($room);

    return $this->sendSuccessResponse('Cập nhật loại phòng thành công', $responseData);
  }

  /**
   * Lấy chi tiết loại phòng
   * GET /api/hostel/room/{id}
   */
  public function editAction($id)
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    if (empty($user->host)) {
      return $this->sendErrorResponse('Bạn chưa đăng ký làm chủ trọ', [], $this->statusCode::FORBIDDEN);
    }

    // Tìm room
    $room = HostelRoomModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $id]
    ]);

    if (empty($room)) {
      return $this->sendErrorResponse('Không tìm thấy loại phòng', [], $this->statusCode::NOT_FOUND);
    }

    // Kiểm tra quyền sở hữu
    if ($room->hostel->host_id != $user->host->id) {
      return $this->sendErrorResponse('Không có quyền truy cập', [], $this->statusCode::FORBIDDEN);
    }

    $responseData = $this->_formatRoomDetailData($room);

    return $this->sendSuccessResponse('OK', $responseData);
  }

  /**
   * Xóa loại phòng
   * DELETE /api/hostel/room/{id}/delete
   */
  public function deleteAction($id)
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    if (empty($user->host)) {
      return $this->sendErrorResponse('Bạn chưa đăng ký làm chủ trọ', [], $this->statusCode::FORBIDDEN);
    }

    // Tìm room
    $room = HostelRoomModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $id]
    ]);

    if (empty($room)) {
      return $this->sendErrorResponse('Không tìm thấy loại phòng', [], $this->statusCode::NOT_FOUND);
    }

    // Kiểm tra quyền sở hữu
    if ($room->hostel->host_id != $user->host->id) {
      return $this->sendErrorResponse('Không có quyền truy cập', [], $this->statusCode::FORBIDDEN);
    }

    // Kiểm tra xem có phòng nào đang sử dụng loại phòng này không
    if ($room->hostRooms && $room->hostRooms->count() > 0) {
      return $this->sendErrorResponse('Không thể xóa loại phòng này vì đang có phòng sử dụng', [], $this->statusCode::BAD_REQUEST);
    }

    try {
      $this->db->begin();

      // Xóa các services liên quan
      if ($room->services) {
        foreach ($room->services as $service) {
          $service->delete();
        }
      }

      // Xóa room
      if (!$room->delete()) {
        throw new \Exception('Không thể xóa loại phòng');
      }

      $this->db->commit();

      return $this->sendSuccessResponse('Xóa loại phòng thành công', []);
    } catch (\Exception $e) {
      $this->db->rollback();
      return $this->sendErrorResponse('Lỗi khi xóa loại phòng: ' . $e->getMessage(), [], $this->statusCode::INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Validate input data cho room - tận dụng validateHostelRoomData từ model
   */
  private function _validateRoomInput($request, $user, $room = null, $uploadedFiles = [])
  {
    $out = ['success' => false, 'message' => '', 'errors' => [], 'hostel' => null];

    // Kiểm tra hostel_id
    if (empty($request['hostel_id'])) {
      $out['message'] = 'Vui lòng chọn trọ';
      return $out;
    }

    $hostel = HostelModel::findFirst([
      'conditions' => 'id = :id: AND host_id = :host_id:',
      'bind' => ['id' => $request['hostel_id'], 'host_id' => $user->host->id]
    ]);

    if (empty($hostel)) {
      $out['message'] = 'Không tìm thấy trọ hoặc bạn không có quyền truy cập';
      return $out;
    }

    // Chuẩn bị data để validate - sử dụng đúng format mà model mong đợi
    $data = [
      'hostel_id' => $hostel->id,
      'title' => $request['title'] ?? '',
      'price' => $request['price'] ?? 0,
      'area' => $request['area'] ?? 0,
      'quantity' => $request['quantity'] ?? 0,
      'maximum' => $request['maximum'] ?? 0,
      'gender' => $request['gender'] ?? '',
      'content' => $request['content'] ?? '',
      'amenities' => $request['amenities'] ?? [],
      'files' => $uploadedFiles,
      'newFiles' => $uploadedFiles
    ];

    // Nếu đang update và có files (uploaded hoặc existing), cho phép validation pass
    if ($room && !empty($uploadedFiles)) {
      // Có files (mới hoặc cũ) nên validation sẽ pass
    } elseif (!$room && empty($uploadedFiles)) {
      // Tạo mới nhưng không có files
      $out['message'] = 'Vui lòng chọn ít nhất 1 hình ảnh';
      return $out;
    }

    // Đánh dấu có files để model validation pass
    if (!empty($uploadedFiles)) {
      $data['files'] = ['has_files'];
      $data['newFiles'] = ['has_files'];
    }

    // Sử dụng validation method có sẵn từ model
    $roomModel = new HostelRoomModel();
    $validation = $roomModel->validateHostelRoomData($data);

    if (!$validation['success']) {
      $out['message'] = $validation['message'];
      $out['errors'] = $validation['errors'] ?? [];
      return $out;
    }

    $out['success'] = true;
    $out['hostel'] = $hostel;
    return $out;
  }

  /**
   * Format dữ liệu room cho response - tận dụng asClientDetail từ model
   */
  private function _formatRoomData($room)
  {
    // Sử dụng asClientDetail method có sẵn trong model
    $baseData = $room->asClientDetail();

    // Thêm các thông tin bổ sung cho API
    $baseData['hostel_id'] = $room->hostel_id;
    $baseData['hostel_name'] = $room->hostel->name ?? '';
    $baseData['status'] = $room->status;
    $baseData['created'] = $room->created;
    $baseData['updated'] = $room->updated;

    // Format lại images với full URL
    if (!empty($baseData['imgs'])) {
      $baseData['imgs'] = array_map(function ($img) {
        return $this->url . $img;
      }, $baseData['imgs']);
    }

    if (!empty($baseData['image'])) {
      $baseData['image'] = $this->url . $baseData['image'];
    }

    return $baseData;
  }

  /**
   * Format dữ liệu chi tiết room cho response (bao gồm thêm thông tin)
   */
  private function _formatRoomDetailData($room)
  {
    // Sử dụng asClientDetail method có sẵn trong model để có đầy đủ thông tin
    $data = $room->asClientDetail();

    // Thêm các thông tin bổ sung cho API
    $data['hostel_id'] = $room->hostel_id;
    $data['status'] = $room->status;
    $data['created'] = $room->created;
    $data['updated'] = $room->updated;
    $data['position'] = $room->position;
    $data['url_review'] = $room->url_review;

    // Thêm thông tin chi tiết về hostel
    $data['hostel'] = [
      'id' => $room->hostel->id,
      'name' => $room->hostel->name,
      'address' => $room->hostel->address,
      'area' => $room->hostel->area,
      'room_total' => $room->hostel->room
    ];

    // Thêm thông tin về số phòng đang sử dụng loại phòng này
    $data['rooms_in_use'] = $room->hostRooms ? $room->hostRooms->count() : 0;

    // Format lại images với full URL
    if (!empty($data['imgs'])) {
      $data['imgs'] = array_map(function ($img) {
        return $this->url . $img;
      }, $data['imgs']);
    }

    if (!empty($data['image'])) {
      $data['image'] = $this->url . $data['image'];
    }

    return $data;
  }

  /**
   * Custom method để xử lý images đã được upload bởi ApiControllerBase
   * Tạo JSON structure tương thích với HostelRoomModel
   */
  private function _processUploadedImages($uploadedPaths, $originFiles = [])
  {
    $imgs = [];

    // Xử lý files mới đã upload
    if (!empty($uploadedPaths)) {
      foreach ($uploadedPaths as $index => $path) {
        $objImg = new \stdClass();
        $objImg->src = $path;
        $objImg->default = ($index == 0) ? 1 : 0;
        $imgs[] = $objImg;
      }
    }

    // Nếu không có files mới, giữ lại files cũ
    if (empty($imgs) && !empty($originFiles)) {
      foreach ($originFiles as $index => $path) {
        $objImg = new \stdClass();
        $objImg->src = $path;
        $objImg->default = ($index == 0) ? 1 : 0;
        $imgs[] = $objImg;
      }
    }

    return [
      'imgs_json' => json_encode($imgs),
      'main_image' => !empty($imgs) ? $imgs[0]->src : null
    ];
  }

  /**
   * Custom method để tạo room với images đã được processed
   */
  private function _createRoomWithProcessedImages($roomData, $uploadedPaths)
  {
    $roomModel = new HostelRoomModel();

    // Validate data trước
    $validation = $roomModel->validateHostelRoomData($roomData);
    if (!$validation['success']) {
      return $validation;
    }

    // Set default values
    $defaults = [
      'hostel_id' => null,
      'title' => null,
      'price' => '',
      'maximum' => '',
      'quantity' => '',
      'area' => '',
      'content' => '',
      'gender' => '',
      'created' => date('Y-m-d H:i:s'),
      'status' => 1
    ];

    $hostelRoomData = array_merge($defaults, $roomData);

    // Assign data
    $roomModel->assign($hostelRoomData);
    $roomModel->content = trim($hostelRoomData['content']);
    $roomModel->updateAmenities($hostelRoomData['amenities'] ?? []);

    // Xử lý images đã upload
    if (!empty($uploadedPaths)) {
      $imageData = $this->_processUploadedImages($uploadedPaths);
      $roomModel->imgs = $imageData['imgs_json'];
      $roomModel->image = $imageData['main_image'];
    }

    // Save
    if ($roomModel->save()) {
      return [
        'success' => true,
        'message' => 'Tạo mới loại phòng thành công',
        'id' => $roomModel->id,
        'hostelRoom' => $roomModel
      ];
    }

    return [
      'success' => false,
      'message' => 'Không thể tạo mới loại phòng',
      'errors' => $roomModel->getMessages()
    ];
  }

  /**
   * Custom method để update room với images đã được processed
   */
  private function _updateRoomWithProcessedImages($roomId, $roomData, $uploadedPaths, $originFiles = [])
  {
    $roomModel = HostelRoomModel::findFirstById($roomId);
    if (!$roomModel) {
      return [
        'success' => false,
        'message' => 'Không tìm thấy loại phòng',
        'errors' => ['id' => 'ID không tồn tại']
      ];
    }

    // Validate data
    $validation = $roomModel->validateHostelRoomData($roomData);
    if (!$validation['success']) {
      return $validation;
    }

    // Assign data
    $roomModel->assign($roomData);
    $roomModel->content = trim($roomData['content']);
    $roomModel->updateAmenities($roomData['amenities'] ?? []);
    $roomModel->updated = date('Y-m-d H:i:s');

    // Cập nhật editor
    if (!empty($roomData['editor_id'])) {
      $roomModel->editor_id = $roomData['editor_id'];
    }

    // Xử lý images
    if (!empty($uploadedPaths)) {
      // Có files mới
      $imageData = $this->_processUploadedImages($uploadedPaths);
      $roomModel->imgs = $imageData['imgs_json'];
      $roomModel->image = $imageData['main_image'];
    } elseif (!empty($originFiles)) {
      // Giữ lại files cũ
      $imageData = $this->_processUploadedImages([], $originFiles);
      $roomModel->imgs = $imageData['imgs_json'];
      $roomModel->image = $imageData['main_image'];
    }

    // Save
    if ($roomModel->save()) {
      return [
        'success' => true,
        'message' => 'Cập nhật loại phòng thành công',
        'hostelRoom' => $roomModel
      ];
    }

    return [
      'success' => false,
      'message' => 'Không thể cập nhật loại phòng',
      'errors' => $roomModel->getMessages()
    ];
  }
}
