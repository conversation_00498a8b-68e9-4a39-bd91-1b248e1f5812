<?php

namespace Modules\Frontend\Controllers\Api;

use Modules\App\Models\AdHostelModel;
use Modules\App\Models\AdHostelPremiumModel;
use Modules\App\Models\AdHostelWholeModel;
use Modules\App\Models\HostelModel;
use Modules\App\Models\UserModel;

/**
 * API Controller cho quản lý Hostel quảng cáo
 */
class AdHostelApiController extends ApiControllerBase
{
  /**
   * Khởi tạo controller
   */
  public function onConstruct()
  {
    parent::onConstruct();
  }

  public function listAction()
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload    = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    if (!$user->host) {
      return $this->sendErrorResponse('Bạn chưa đăng ký làm chủ trọ', [], $this->statusCode::FORBIDDEN);
    }

    // Lấy các tham số lọc và phân trang
    $page       = $this->request->getQuery('page') ?: 1;
    $perPage    = $this->request->getQuery('per_page') ?: 10;
    $type       = $this->request->getQuery('type') ?: 'all';
    $sort       = $this->request->getQuery('sort') ?: 'created';
    $sortType   = $this->request->getQuery('sort_type') ?: 'DESC';

    $arrType = [
      'all' => '',
      'hot' => [
        'conditions'  => 'HP.id IS NOT NULL AND HP.expired >= NOW() AND HP.premium_type = :type:',
        'bind'        => ['type' => 'hot', 'status' => 1]
      ],
      'normal' => [
        'conditions' => '(HP.id IS NULL OR HP.expired < NOW() OR HP.premium_type = :type:)',
        'bind'       => ['type' => 'normal', 'status' => 1]
      ],
      'active' => [
        'conditions' => 'AH.is_published = :is_published: AND AH.locked = :locked:',
        'bind'       => ['is_published' => 1, 'locked' => 0]
      ],
      'close' => [
        'conditions' => 'AH.is_published = :is_published: ',
        'bind'       => ['is_published' => 0]
      ],
      'locked' => [
        'conditions' => 'AH.locked = :locked:',
        'bind'       => ['locked' => 1]
      ],
    ];

    $conditions = [];
    $bindParams = [];

    // Xử lý điều kiện sắp xếp
    if ($sort === 'name') {
      $sort = 'H.name ' . $sortType;
    } elseif ($sort === 'created') {
      $sort = 'AH.created ' . $sortType;
    } else {
      $sort = 'AH.published ' . $sortType;
    }

    if ($type !== 'all' && isset($arrType[$type])) {
      $conditions[] = $arrType[$type]['conditions'];
      $bindParams = array_merge($bindParams, $arrType[$type]['bind']);
    }

    if ($user->host) {
      $hostels   = $user->host->getHostels()->toArray();
      $hostelIds = array_column($hostels, 'id');

      if (empty($hostelIds)) {
        return $this->sendSuccessResponse('Danh sách quảng cáo rỗng', [], $this->statusCode::NOT_FOUND);
      }

      $conditions[]             = 'AH.hostel_id IN ({hostelIds:array})';
      $bindParams['hostelIds']  = $hostelIds;
    }

    $where = '';
    if (!empty($conditions)) {
      $where = implode(' AND ', array_filter($conditions));
    }

    $params = [
      'where'     => $where . ' AND H.status = :status:',
      'bind'      => $bindParams + ['status' => 1],
      'order'     => $sort
    ];

    $adHostels = AdHostelModel::init()->GetItem($params);

    if (empty($adHostels) || $adHostels->count() == 0) {
      return $this->sendSuccessResponse('Danh sách quảng cáo rỗng', [], $this->statusCode::NOT_FOUND);
    }

    $data      = $this->getPagination($adHostels, $perPage, $page);

    $pagination = [
      'page'            => empty($data) ? 0 : (int)$data->getCurrentPage(),
      'per_page'        => $adHostels->count() > $perPage ? (int)$perPage : (int)$adHostels->count(),
      'total_page'      => empty($data) ? 0 : (int)$data->getLastPage() ?? 0,
      'total_item'      => (int)$adHostels->count() ?? 0,
    ];

    $items = [];
    foreach ($data as $value) {
      $items[] = $this->_getAdHostelListItem($value);
    }

    $result = [
      'items'       => $items,
      'pagination'  => $pagination,
    ];
    return $this->sendSuccessResponse('OK', $result);
  }

  public function detailAction($id)
  {
    $this->view->disable();
    $authHeader = $this->request->getHeader('Authorization');
    $payload = $this->getPayload($authHeader);

    if (empty($payload) || empty($payload->userId)) {
      return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // Lấy thông tin người dùng
    $user = UserModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $payload->userId]
    ]);

    if (empty($user)) {
      return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_UNAUTHORIZED);
    }

    // Lấy thông tin hostel quảng cáo
    $adHostel = AdHostelModel::findFirst([
      'conditions' => 'id = :id:',
      'bind' => ['id' => $id]
    ]);

    if (empty($adHostel)) {
      return $this->sendErrorResponse('Không tìm thấy thông tin trọ quảng cáo', [], $this->statusCode::NOT_FOUND);
    }

    if ($user->host) {
      $hostel = HostelModel::findFirst([
        'conditions' => 'id = :id: AND host_id = :host_id:',
        'bind' => ['id' => $adHostel->hostel_id, 'host_id' => $user->host->id]
      ]);

      if (empty($hostel)) {
        return $this->sendErrorResponse('Bạn không có quyền xem thông tin này', [], $this->statusCode::FORBIDDEN);
      }
    }

    $data = $adHostel->asClientDetail();

    // Trả về kết quả
    return $this->sendSuccessResponse('Thông tin chi tiết trọ quảng cáo', $data);
  }

  public function addAction()
  {
    $this->view->disable();
    try {
      $this->db->begin();
      $authHeader = $this->request->getHeader('Authorization');
      $payload    = $this->getPayload($authHeader);

      if (empty($payload) || empty($payload->userId)) {
        throw new \Exception('Token hết hạn hoặc không hợp lệ', $this->statusCode::BAD_UNAUTHORIZED);
      }

      $user = UserModel::findFirst([
        'conditions'  => 'id = :id:',
        'bind'        => ['id' => $payload->userId]
      ]);

      if (empty($user)) {
        throw new \Exception('Người dùng không tồn tại', $this->statusCode::BAD_UNAUTHORIZED);
      }

      $formData       = $this->request->getPost();
      $uploadedFiles  = $this->request->getUploadedFiles();

      $validate = $this->validateImages($uploadedFiles);
      if (!empty($validate['invalid'])) {
        throw new \Exception(json_encode($validate['invalid']));
      }

      $this->_uploadImage($uploadedFiles, self::ACTION_CREATE);

      if ($user->host && !empty($formData['hostel_id'])) {
        $hostel = HostelModel::findFirst([
          'conditions'  => 'id = :id: AND host_id = :host_id:',
          'bind'        => ['id' => $formData['hostel_id'], 'host_id' => $user->host->id]
        ]);

        if (empty($hostel)) {
          throw new \Exception('Bạn không có quyền tạo quảng cáo cho trọ này', $this->statusCode::FORBIDDEN);
        }

        if ($hostel->adHostel) {
          throw new \Exception('Trọ này đã có quảng cáo', $this->statusCode::BAD_REQUEST);
        }
      }

      $formData['creator_id'] = $user->id;
      $result = AdHostelModel::init()->createAdHostel($formData);

      if (!$result['success']) {
        if (!empty($result['errors'])) {
          foreach ($result['errors'] as $key => $message) {
            throw new \Exception($message);
          }
        }
        throw new \Exception($result['message']);
      }

      if (!empty($formData['whole'])) {
        $whole                = new AdHostelWholeModel();
        $whole->ad_hostel_id  = $result['id'];
        $whole->livingroom    = $formData['whole']['livingroom'] ?? 0;
        $whole->kitchen       = $formData['whole']['kitchen'] ?? 0;
        $whole->bathroom      = $formData['whole']['bathroom'] ?? 0;
        $whole->bedroom       = $formData['whole']['bedroom'] ?? 0;
        $whole->created       = date('Y-m-d H:i:s');

        if (!$whole->save()) {
          throw new \Exception('Tạo thông tin whole thất bại: ' . implode(', ', $whole->getMessages()));
        }
      }

      $this->db->commit();

      return $this->sendSuccessResponse('Tạo mới trọ quảng cáo thành công', [
        'id' => $result['id'],
        'hostel' => [
          'name'    => $result['hostel']->hostel->name,
          'created' => date('d-m-Y', strtotime($result['hostel']->created))
        ]
      ], $this->statusCode::CREATED);
    } catch (\Exception $e) {
      $this->db->rollback();
      return $this->sendErrorResponse($e->getMessage(), [], $this->statusCode::BAD_REQUEST);
    }
  }

  public function updateAction($id)
  {
    $this->view->disable();
    try {
      $this->db->begin();
      $authHeader = $this->request->getHeader('Authorization');
      $payload = $this->getPayload($authHeader);

      if (empty($payload) || empty($payload->userId)) {
        throw new \Exception('Token hết hạn hoặc không hợp lệ', $this->statusCode::BAD_UNAUTHORIZED);
      }

      $user = UserModel::findFirst([
        'conditions' => 'id = :id:',
        'bind' => ['id' => $payload->userId]
      ]);

      if (empty($user)) {
        throw new \Exception('Người dùng không tồn tại', $this->statusCode::BAD_UNAUTHORIZED);
      }

      $adHostel = AdHostelModel::findFirst([
        'conditions' => 'id = :id:',
        'bind' => ['id' => $id]
      ]);

      if (empty($adHostel)) {
        throw new \Exception('Không tìm thấy thông tin trọ quảng cáo', $this->statusCode::NOT_FOUND);
      }

      if ($user->host) {
        $hostel = HostelModel::findFirst([
          'conditions' => 'id = :id: AND host_id = :host_id:',
          'bind' => ['id' => $adHostel->hostel_id, 'host_id' => $user->host->id]
        ]);

        if (empty($hostel)) {
          throw new \Exception('Bạn không có quyền cập nhật quảng cáo này', $this->statusCode::FORBIDDEN);
        }
      }

      $formData = $this->request->getPost();
      $uploadedFiles = $this->request->getUploadedFiles();

      $validate = $this->validateImages($uploadedFiles);
      if (!empty($validate['invalid'])) {
        throw new \Exception(json_encode($validate['invalid']));
      }

      if (!isset($formData['originFiles']) || empty($formData['originFiles'])) {
        $formData['files'] = $uploadedFiles;
      } else {
        $formData['newFiles'] = $uploadedFiles;
      }

      $formData['ad_hostel_id'] = $id;
      $formData['editor_id'] = $user->id;

      $result = AdHostelModel::init()->updateAdHostel($id, $formData);

      if (!$result['success']) {
        if (!empty($result['errors'])) {
          foreach ($result['errors'] as $key => $message) {
            throw new \Exception($message);
          }
        }
        throw new \Exception($result['message']);
      }

      if (!empty($formData['whole'])) {
        $whole = AdHostelWholeModel::findFirst([
          'conditions' => 'ad_hostel_id = :ad_hostel_id:',
          'bind' => ['ad_hostel_id' => $id]
        ]);

        if (empty($whole)) {
          $whole = new AdHostelWholeModel();
          $whole->ad_hostel_id = $id;
          $whole->created = date('Y-m-d H:i:s');
        } else {
          $whole->updated = date('Y-m-d H:i:s');
        }

        $whole->livingroom = $formData['whole']['livingroom'] ?? 0;
        $whole->kitchen = $formData['whole']['kitchen'] ?? 0;
        $whole->bathroom = $formData['whole']['bathroom'] ?? 0;
        $whole->bedroom = $formData['whole']['bedroom'] ?? 0;

        if (!$whole->save()) {
          throw new \Exception('Cập nhật thông tin whole thất bại: ' . implode(', ', $whole->getMessages()));
        }
      }

      $this->db->commit();

      return $this->sendSuccessResponse('Cập nhật trọ quảng cáo thành công', [
        'id' => $id,
        'hostel' => [
          'name' => $result['hostel']->hostel->name,
          'updated' => date('d-m-Y', strtotime($result['hostel']->updated))
        ]
      ]);
    } catch (\Exception $e) {
      $this->db->rollback();
      return $this->sendErrorResponse($e->getMessage(), [], $this->statusCode::BAD_REQUEST);
    }
  }

  public function togglePublishAction($id)
  {
    $this->view->disable();
    
    try {
      $this->db->begin();
      
      // Xác thực token và lấy user
      $payload = $this->getPayload($this->request->getHeader('Authorization'));
      if (empty($payload) || empty($payload->userId)) {
        throw new \Exception('Token hết hạn hoặc không hợp lệ', $this->statusCode::BAD_UNAUTHORIZED);
      }

      $user = UserModel::findFirst([
        'conditions' => 'id = :id:',
        'bind' => ['id' => $payload->userId]
      ]);

      if (empty($user)) {
        throw new \Exception('Người dùng không tồn tại', $this->statusCode::BAD_UNAUTHORIZED);
      }

      // Lấy thông tin ad hostel
      $adHostel = AdHostelModel::findFirst([
        'conditions' => 'id = :id:',
        'bind' => ['id' => $id]
      ]);

      if (empty($adHostel)) {
        throw new \Exception('Không tìm thấy thông tin trọ quảng cáo', $this->statusCode::NOT_FOUND);
      }

      // Kiểm tra quyền sở hữu
      if ($user->host) {
        $hostel = HostelModel::findFirst([
          'conditions' => 'id = :id: AND host_id = :host_id:',
          'bind' => ['id' => $adHostel->hostel_id, 'host_id' => $user->host->id]
        ]);

        if (empty($hostel)) {
          throw new \Exception('Bạn không có quyền cập nhật quảng cáo này', $this->statusCode::FORBIDDEN);
        }
      }

      // Chuyển đổi trạng thái publish
      $adHostel->is_published = $adHostel->is_published == 1 ? 0 : 1;
      $adHostel->updated      = date('Y-m-d H:i:s');

      if (!$adHostel->save()) {
        throw new \Exception('Cập nhật trạng thái quảng cáo thất bại: ' . implode(', ', $adHostel->getMessages()));
      }

      $this->db->commit();

      return $this->sendSuccessResponse('Cập nhật trạng thái quảng cáo thành công', [
        'ad-hostel' => $adHostel->asHostListItem()
      ]);
      
    } catch (\Exception $e) {
      $this->db->rollback();
      return $this->sendErrorResponse($e->getMessage(), [], $this->statusCode::BAD_REQUEST);
    }
  }

  private function _getAdHostelListItem($adHostel)
  {
    return $adHostel->asHostListItem();
  }
}
