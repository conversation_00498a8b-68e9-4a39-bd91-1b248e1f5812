<?php

namespace Modules\App\Models;

use Phalcon\Mvc\Model;
use Phalcon\Mvc\Model\Query;
use Phalcon\Mvc\Model\Resultset\Simple as Resultset;
use Phalcon\Mvc\Model\Manager as ModelsManager;

class AdHostelModel extends DatatableManagerModel
{
	public function getSource()
	{
		return "ad_hostel";
	}

	public function columnMap()
	{
		return array(
			'id'                =>  'id',
			'hostel_id'         =>  'hostel_id',
			'imgs'              =>  'imgs',
			'url_review'				=>  'url_review',
			'content'           =>  'content',
			'managing'          =>  'managing',
			'amenities'         =>  'amenities',
			'environment'       =>  'environment',
			'object_type'       =>  'object_type',
			'price'         		=>  'price',
			'price_sale' 				=>  'price_sale',
			'sale_expired' 			=>  'sale_expired',
			'sticked'						=>	'sticked',
			'is_published'      =>  'is_published',
			'locked'            =>  'locked',
			'hits'              =>  'hits',
			'published'         =>  'published',
			'created'           =>  'created',
			'updated'           =>  'updated',
			'creator_id'        =>  'creator_id',
			'editor_id'         =>  'editor_id',
			'name_info'         =>  'name_info',
			'phone'        			=>  'phone',
			'zalo'        			=>  'zalo',
			'position'          =>  'position',
			'seo_title'         =>  'seo_title',
			'seo_description'   =>  'seo_description',
			'seo_keywords'      =>  'seo_keywords',
			'seo_image'         =>  'seo_image',
		);
	}

	public function initialize()
	{
		$this->belongsTo('hostel_id', '\Modules\App\Models\HostelModel', 'id', ['alias' =>	'hostel']);
		$this->belongsTo('creator_id', '\Modules\App\Models\ErpMemberModel', 'id', ['alias' => 'creator', 'reusable' => true]);
		$this->hasOne('id', '\Modules\App\Models\AdHostelPremiumModel', 'ad_hostel_id', ['alias' => 'premium', 'reusable' => true]);
		$this->hasMany('id', '\Modules\App\Models\AdHostelSavedModel', 'ad_hostel_id', ['alias' => 'saved']);
		$this->hasMany('id', '\Modules\App\Models\AdHostelHitsModel', 'ad_hostel_id', ['alias' => 'hits', 'reusable'  => true]);
		$this->hasMany('id', '\Modules\App\Models\AdHostelLocationDistanceModel', 'ad_hostel_id', ['alias' => 'adHostel_Location_Distances']);
		$this->hasOne('id', '\Modules\App\Models\AdHostelWholeModel', 'ad_hostel_id', ['alias' => 'whole']);
	}

	public static function init()
	{
		return new self();
	}

	public function datatable_column($dataParams = null)
	{
		$init_data = array(
			array(
				'name' => 'id',
				'label' => 'ID',
				'width' => '50px',
				'filter' => array(
					'type' => 'text'
				)
			),
			array(
				'name' => 'hostel_name',
				'label' => 'Hostel',
				'width' => '200px',
				'filter' => array(
					'type' => 'text'
				)
			),
			array(
				'name' => 'price',
				'label' => 'Giá',
				'width' => '100px',
				'filter' => array(
					'type' => 'text'
				)
			),
			array(
				'name' => 'hits',
				'label' => 'Lượt xem',
				'width' => '80px',
				'filter' => array(
					'type' => 'text'
				)
			),
			array(
				'name' => 'sticked',
				'label' => 'Xác thực',
				'width' => '80px',
				'filter' => array(
					'type' => 'select',
					'value' => array(0 => 'Không', 1 => 'Có')
				)
			),
			array(
				'name' => 'is_published',
				'label' => 'Đã xuất bản',
				'width' => '80px',
				'filter' => array(
					'type' => 'select',
					'value' => array(0 => 'Không', 1 => 'Có')
				)
			),
			array(
				'name' => 'locked',
				'label' => 'Đã khóa',
				'width' => '80px',
				'filter' => array(
					'type' => 'select',
					'value' => array(0 => 'Không', 1 => 'Có')
				)
			),
			array(
				'name' => 'created',
				'label' => 'Ngày tạo',
				'width' => '120px',
				'filter' => array(
					'type' => 'text'
				)
			),
			array(
				'name' => 'action',
				'label' => 'Thao tác',
				'width' => '120px',
				'filter' => null
			)
		);
		return $init_data;
	}

	public function my_url($_format, $id = null)
	{
		$Ohi = new \Modules\Library\Oh\Ohi();
		return $Ohi->baseAdminUrl("ad-hostel/index/{$_format}/{$id}");
	}

	public function dt_init($dataParams = null)
	{
		return array(
			'init_data' => $this->datatable_column(),
			'data_url' => '/admin/ad-hostel/index/datatable'
		);
	}

	public function json_data($dataParams = null)
	{
		$result = $this->datatable_find([
			'select' => ['AH.*', 'H.name as hostel_name'],
			'from' => ['AH' => 'Modules\App\Models\AdHostelModel'],
			'leftJoin' => [
				'Modules\App\Models\HostelModel' => ['alias' => 'H', 'on' => 'AH.hostel_id = H.id']
			]
		]);

		$result->json_data = array();

		foreach ($result->realData as $item) {
			$action = array(
				'view' => array('href' => 'javascript:void(0)', 'text' => 'Chi tiết', 'onclick' => 'showAdHostelDetail(' . $item->id . ')'),
				'edit' => array('href' => '/admin/ad-hostel/index/update/' . $item->id, 'text' => 'Sửa'),
				'delete' => array('href' => 'javascript:void(0)', 'text' => 'Xóa', 'onclick' => 'deleteAdHostel(' . $item->id . ')')
			);

			$hostel = HostelModel::findFirstById($item->hostel_id);
			$hostel_name = $hostel ? $hostel->name : 'N/A';

			$result->json_data[] = array(
				'id' => $item->id,
				'hostel_name' => $hostel_name,
				'price' => number_format($item->price) . ' VNĐ',
				'hits' => $item->hits,
				'sticked' => $this->changeValue('sticked', $item->id, $item->sticked, 'ad-hostel'),
				'is_published' => $this->changeValue('is_published', $item->id, $item->is_published, 'ad-hostel'),
				'locked' => $this->changeValue('locked', $item->id, $item->locked, 'ad-hostel'),
				'created' => date('d/m/Y H:i', strtotime($item->created)),
				'action' => $this->renderHtmlButton($action)
			);
		}

		return array(
			'draw' => $result->draw,
			'data' => $result->json_data,
			'recordsTotal' => $result->total,
			'recordsFiltered' => $result->totalRealData
		);
	}

	public function GetItem($params = null, $option = null)
	{
		$builder = $this->modelsManager->createBuilder()
			->columns(['AH.*',])
			->from(['AH' => 'Modules\App\Models\AdHostelModel'])
			->leftJoin('Modules\App\Models\HostelModel', 'H.id = AH.hostel_id', 'H')
			->leftJoin('Modules\App\Models\AdHostelPremiumModel', 'HP.ad_hostel_id = AH.id', 'HP')
			->leftJoin('Modules\App\Models\HostModel', 'HS.id = H.host_id', 'HS')
			->leftJoin('Modules\App\Models\UserModel', 'U.id = HS.user_id', 'U')
			->leftJoin('Modules\App\Models\HostelRoomModel', 'R.hostel_id = H.id', 'R')
			->leftJoin('Modules\App\Models\AdHostelLocationDistanceModel', 'AHLD.ad_hostel_id = AH.id', 'AHLD')
			->leftJoin('Modules\App\Models\LocationAreaModel', 'LA.id = AHLD.location_id', 'LA');

		if (!empty($params['where'])) {
			$builder->where($params['where']);
		}

		if (!empty($params['order'])) {
			$builder->orderBy($params['order']);
		} else {
			$builder->orderBy("
				IF(HP.expired >= NOW() AND HP.premium_type = 'hot', AH.published, 0) DESC,
				AH.published DESC
			");
		}

		if (!empty($params['limit'])) {
			$builder->limit($params['limit']);
		}

		if (!empty($params['offset'])) {
			$builder->offset($params['offset']);
		}

		$builder->groupBy("AH.id");

		$params['bind'] = !empty($params['bind']) ? $params['bind'] : null;

		$result = $builder->getQuery()->execute($params['bind']);
		return $result;
	}

	public function GetItemIds($params = null)
	{
		$builder = $this->modelsManager->createBuilder()
			->columns(['AH.id'])
			->from(['AH' => 'Modules\App\Models\AdHostelModel'])
			->leftJoin('Modules\App\Models\HostelModel', 'H.id = AH.hostel_id', 'H')
			->leftJoin('Modules\App\Models\AdHostelPremiumModel', 'HP.ad_hostel_id = AH.id', 'HP');

		if (!empty($params['where'])) {
			$builder->where($params['where']);
		}

		if (!empty($params['order'])) {
			$builder->orderBy($params['order']);
		} else {
			$builder->orderBy("
				IF(HP.expired >= NOW() AND HP.premium_type = 'hot', AH.published, 0) DESC,
				AH.published DESC
			");
		}

		if (!empty($params['limit'])) {
			$builder->limit($params['limit']);
		}

		if (!empty($params['offset'])) {
			$builder->offset($params['offset']);
		}

		$builder->groupBy("AH.id");

		$params['bind'] = !empty($params['bind']) ? $params['bind'] : null;

		$result = $builder->getQuery()->execute($params['bind']);

		$ids = [];
		foreach ($result as $item) {
			$ids[] = (int)$item->id;
		}

		return $ids;
	}

	/**
	 * Tạo mới một bản ghi hostel
	 * 
	 * @param array $data Dữ liệu nhà trọ cần tạo
	 * @return array Kết quả tạo bản ghi (thành công/thất bại, thông tin lỗi nếu có)
	 */
	public function createAdHostel($data)
	{
		// 1. Kiểm tra dữ liệu đầu vào
		$validation = $this->validateAdHostelData($data);
		if (!$validation['success']) {
			return $validation;
		}

		// 2. Set default value
		$defaults = [
			'hostel_id'		=> null,
			'managing'    => 0,
			'price'       => 0,
			'amenities'   => '',
			'environment' => '',
			'object_type' => '',
			'name_info'   => '',
			'phone' 			=> '',
			'zalo' 				=> '',
			'is_published' => 1,
			'locked'      => 0,
			'created'			=> date('Y-m-d H:i:s')
		];

		// 3. Gộp dữ liệu
		$adHostelData = array_merge($defaults, $data);

		// 4. Gán dữ liệu cho model
		$this->assign($adHostelData);
		$this->content = trim($adHostelData['content']);
		$this->updateProps($data['object_type'], $data['amenities'], $data['environment']);
		$this->updateImage($data['files'] ?? [], $data['renameFiles'] ?? [], [], []);

		// 5. Lưu bản ghi
		if ($this->save()) {
			// 6. (Có thể mở rộng: tạo HostelInfo, log lịch sử, gửi thông báo...)
			// Tạm thời có thể chưa cần thiết
			return [
				'success' => true,
				'message' => 'Tạo mới tin thành công',
				'id'      => $this->id,
				'hostel'  => $this // Trả về object model để xử lý tiếp
			];
		}

		return [
			'success' => false,
			'message' => 'Không thể tạo mới tin',
			'errors'  => $this->getMessages()
		];
	}

	public function validateAdHostelData($data)
	{
		$errors = [];
		$phoneRegex = '/^(03|05|07|08|09|01[2|6|8|9])([0-9]{8})$/';

		$hostel = HostelModel::findFirstById($data['hostel_id'] ?? null);
		if (!$hostel) $errors['hostel_id'] = 'Trọ không tồn tại';

		if (empty($data['price'])) {
			$errors['price'] = 'Giá cho thuê không được để trống';
		}

		if (!isset($data['object_type']) || empty($data['object_type'])) {
			$errors['object_type'] = 'Vui lòng chọn đối tượng';
		}

		if (!isset($data['amenities']) || empty($data['amenities'])) {
			$errors['amenities'] = 'Vui lòng chọn tiện nghi';
		}

		if (!isset($data['environment']) || empty($data['environment'])) {
			$errors['environment'] = 'Vui lòng chọn môi trường xung quanh';
		}

		if (empty($data['content'])) {
			$errors['content'] = 'Mô tả không được để trống';
		}

		if (!empty($data['contact_option'])) {
			if (empty($data['name_info'])) {
				$errors['name_info'] = 'Họ tên không được để trống';
			}

			if (empty($data['phone']) || !preg_match($phoneRegex, $data['phone'])) {
				$errors['phone'] = 'Số điện thoại không hợp lệ';
			}

			if (empty($data['zalo']) || !preg_match($phoneRegex, $data['zalo'])) {
				$errors['zalo'] = 'Zalo không hợp lệ';
			}
		}

		if (!empty($errors)) {
			return [
				'success' => false,
				'message' => 'Dữ liệu không hợp lệ',
				'errors'  => $errors
				// Error: Có thể dùng key => value để hiển thị lỗi
			];
		}

		return ['success' => true];
	}

	public function updateProps($object_type, $amenities, $environment)
	{
		$this->object_type = json_encode(array_fill_keys($object_type, "1"));
		$this->amenities = json_encode(array_fill_keys($amenities, "1"));
		$this->environment = json_encode(array_fill_keys($environment, "1"));
	}

	public function updateImage($files, $renameFiles, $originFiles, $newFile)
	{
		$targetDir = ROOT_PATH . '/public/uploads/guest/';
		$arrImgs = [];

		$deletedFiles = array_diff($originFiles, $files);
		foreach ($deletedFiles as $deletedFile) {
			$filePath = $targetDir . basename($deletedFile);
			if (file_exists($filePath)) unlink($filePath);
		}

		$arrImgs = [];
		$uploadedFiles = $files;
		if (!empty($originFiles)) {
			$arrImgs = $files;
			$uploadedFiles = $newFile;
		}

		foreach ($uploadedFiles as $key => $file) {
			$name = $renameFiles[$key];
			$targetFile = $targetDir . $name;
			$file->moveTo($targetFile);
			$arrImgs[] = '/uploads/guest/' . $name;
		}

		$imgs = [];
		foreach ($arrImgs as $keys => $img) {
			if (!empty($img)) {
				$objImg = new \stdClass();
				$objImg->src = $img;
				$objImg->default = ($keys == 0) ? 1 : 0;
				$imgs[] = $objImg;
			}
		}

		$this->imgs = json_encode($imgs);
	}

	public function thumb()
	{
		$imgs = json_decode($this->imgs);
		if (empty($imgs)) {
			return $this->hostel->image;
		}

		$thumb = $imgs[0]->src;

		foreach ($imgs as $img) {
			if (!empty($img->default)) {
				$thumb = $img->src;
				break;
			}
		}

		return !empty($thumb) ? $thumb : $this->hostel->image;
	}

	public function parseImgs($imgs)
	{
		foreach ($arrImgs as $keys => $img) {
			if (!empty($img)) {
				$objImg = new \stdClass();
				$objImg->src = $img;
				$objImg->default = ($keys == 0) ? 1 : 0;
				$imgs[] = $objImg;
			}
		}
		$this->imgs = json_encode($imgs);
		return $this->save();
	}

	/**
	 * Cập nhật thông tin nhà trọ
	 * 
	 * @param int $id ID nhà trọ cần cập nhật
	 * @param array $data Dữ liệu cập nhật
	 * @return array Kết quả cập nhật
	 */
	public function updateAdHostel($id, $data)
	{
		// Tìm bản ghi cần cập nhật
		$adHostel = self::findFirstById($id);
		if (!$adHostel) {
			return [
				'success' => false,
				'message' => 'Không tìm thấy tin',
				'errors'  => ['id' => 'ID không tồn tại']
			];
		}

		$validation = $this->validateAdHostelData($data, $id);
		if (!$validation['success']) {
			return $validation;
		}

		// Gán dữ liệu mới
		$adHostel->assign($data);

		// Cập nhật người sửa
		if (!empty($data['editor_id'])) $adHostel->editor_id = $data['editor_id'];
		if (empty($data['contact_option'])) {
			$adHostel->name_info 	= null;
			$adHostel->phone 			= null;
			$adHostel->zalo 			= null;
		}
		$adHostel->updated = date('Y-m-d H:i:s');
		$adHostel->content = trim($data['content']);
		$adHostel->updateProps($data['object_type'], $data['amenities'], $data['environment']);
		$adHostel->updateImage($data['files'] ?? [], $data['renameFiles'] ?? [], $data['originFiles'] ?? [], $data['newFiles'] ?? []);

		// Lưu lại
		if ($adHostel->save()) {
			return [
				'success' => true,
				'message' => 'Cập nhật tin thành công',
				'id'      => $adHostel->id,
				'hostel'  => $adHostel
			];
		}

		return [
			'success' => false,
			'message' => 'Không thể cập nhật tin',
			'errors'  => $adHostel->getMessages()
		];
	}

	public function pushHostel()
	{
		$this->published = date('Y-m-d H:i:s');
		return $this->save();
	}

	public function isPremium()
	{
		return $this->premium && !$this->premium->isExpired() ? true : false;
	}

	public function hitUp()
	{
		$this->hits = $this->hits + 1;
		return $this->save();
	}

	public function asClientListItemResource($userId = null)
	{
		$result = [
			'id' 						=> $this->id,
			'title' 				=> $this->hostel->name,
			'image' 				=> $this->thumb(),
			'price' 				=> $this->price,
			'type' 					=> [
				'id' 					=> $this->hostel->hostel_type->id,
				'name' 				=> $this->hostel->hostel_type->title,
			],
			'area' 					=> $this->hostel->area,
			'province' 			=> [
				'code' 				=> $this->hostel->province->code,
				'name' 				=> $this->hostel->province->name,
			],
			'district' 			=> [
				'code' 				=> $this->hostel->district->code,
				'name' 				=> $this->hostel->district->name,
			],
			'ward' 					=> [
				'code' 				=> $this->hostel->ward->code,
				'name' 				=> $this->hostel->ward->name,
			],
			'address' 			=> $this->hostel->address,
			'premium' 			=> $this->isPremium() ? [
				'type' 			=> $this->premium->premium_type,
				'expired' 	=> date('d/m/Y H:i', strtotime($this->premium->expired)),
			] : null,
			'sale'					=> null,
			'url_review' 		=> $this->url_review,
			'is_favorite' 	=> $this->isFavorite($userId),
		];
		return $result;
	}

	public function asHostListItem()
	{
		$result = [
			'id' 						=> $this->id,
			'title' 				=> $this->hostel->name,
			'image' 				=> $this->thumb(),
			'price' 				=> $this->price,
			'address' 			=> $this->hostel->address,
			'status' 				=> $this->statusResponse($this->parseStatus()),
			'premium' 			=> $this->isPremium() ? [
				'type' 				=> $this->premium->premium_type,
				'expired' 		=> date('d/m/Y H:i', strtotime($this->premium->expired)),
			] : null,
			'hits' 						=> $this->hits,
			'rooms' 					=> $this->listRoom(),
			'position' 				=> $this->countPosition()
		];
		return $result;
	}

	public function asClientDetail($userId = null)
	{
		$result = [
			'id' 						=> $this->id,
			'title' 				=> $this->hostel->name,
			'type' 					=> [
				'id' 					=> $this->hostel->hostel_type->id,
				'name' 				=> $this->hostel->hostel_type->title,
			],
			'image' 				=> $this->thumb(),
			'price' 				=> $this->price,
			'address' 			=> $this->hostel->address,
			'area' 					=> $this->hostel->area,
			'total_room' 		=> $this->hostel->room > 0 ? $this->hostel->room : null,
			'sticked' 			=> $this->sticked,
			'url_review' 		=> $this->url_review,
			'map' 					=> $this->map(),
			'premium' 			=> $this->isPremium() ? [
				'type' 			=> $this->premium->premium_type,
				'expired' 	=> date('d/m/Y H:i', strtotime($this->premium->expired)),
			] : null,
			'imgs' 					=> $this->getImgs(),
			'isFavorite' 		=> $this->isFavorite($userId),
			'content' 			=> htmlentities($this->content),
			'amenities' 		=> $this->getProps($this->amenities),
			'environment' 	=> $this->getProps($this->environment),
			'object_type' 	=> $this->getProps($this->object_type),
			'whole' 				=> $this->getWhole() ?? null,
			'room' 					=> $this->listRoom() ?? [],
			'distance' 			=> $this->distance(),
			'created' 			=> date('d/m/Y H:i', strtotime($this->created)),
			'host'          => [
				'name' 			=> empty($this->name_info) ? $this->hostel->host->name : $this->name_info,
				'phone' 		=> empty($this->phone) ? $this->hostel->host->phone : $this->phone,
				'zalo' 			=> empty($this->zalo) ? $this->hostel->host->zalo : $this->zalo,
			]
		];

		return $result;
	}

	private function getImgs()
	{
		$imgs = json_decode($this->imgs);
		$result = [];
		foreach ($imgs as $img) {
			$result[] = $img->src;
		}
		return $result;
	}

	private function getProps($props)
	{
		$props = json_decode($props);
		$result = [];
		foreach ($props as $key => $value) {
			$prop = AdHostelPropertiesModel::findFirstByCode($key);
			$result[] = [
				'key' 		=> $key,
				'value' 	=> $prop->title,
				'icon' 		=> $prop->icon_image,
			];
		}
		return $result;
	}

	private function isFavorite($userId)
	{
		if (empty($userId)) return false;
		$favorite = AdHostelSavedModel::findFirst([
			'conditions' => 'user_id = :user_id: AND ad_hostel_id = :ad_hostel_id:',
			'bind' => ['user_id' => $userId, 'ad_hostel_id' => $this->id]
		]);
		return $favorite ? true : false;
	}

	private function listRoom()
	{
		$rooms = $this->hostel->rooms;
		$result = [];
		foreach ($rooms as $room) {
			$result[] = $room->asClientDetail();
		}
		return $result;
	}

	private function getWhole()
	{
		$whole = $this->whole;
		if (empty($whole)) return null;
		if ($whole->livingroom == 0 && $whole->kitchen == 0 && $whole->bathroom == 0 && $whole->bedroom == 0) {
			return null;
		}
		$result = [];
		$result['livingroom'] = $whole->livingroom;
		$result['kitchen'] = $whole->kitchen;
		$result['bathroom'] = $whole->bathroom;
		$result['bedroom'] = $whole->bedroom;
		return $result;
	}

	private function map()
	{
		if (empty($this->hostel->latitude) || empty($this->hostel->longitude)) return null;
		return 'https://www.google.com/maps?q=' . $this->hostel->latitude . ',' . $this->hostel->longitude;
	}

	private function distance()
	{
		$locationDistance = $this->adHostel_Location_Distances ?? [];
		if (empty($locationDistance)) return null;
		$result = [];
		foreach ($locationDistance as $distance) {
			$result[] = [
				'name' 			=> $distance->area->name,
				'distance' 	=> $distance->distance,
			];
		}
		return $result;
	}

	private function countPosition()
	{
		$allHostel = AdHostelModel::GetItemIds([
			'where' => 'AH.is_published = :is_published:',
			'bind' => [
				'is_published' => 1,
			],
		]);

		if (empty($allHostel)) {
			return 0;
		}

		$position = array_search($this->id, $allHostel);

		return $position !== false ? $position + 1 : null;
	}


	private function parseStatus()
	{
		$hostelStatus 	= $this->hostel->status;
		$isPublished 		= $this->is_published;
		$locked 				= $this->locked;

		if ($hostelStatus == 0) {
			return 'close';
		}

		if ($isPublished == 0) {
			return 'not_active';
		}

		if ($locked == 1) {
			return 'locked';
		}

		if ($isPublished == 1) {
			return 'published';
		}

		return 'open';
	}

	private function statusResponse($status)
	{
		$arrayStatus = [
			'close' 			=> 'Đã đóng',
			'open' 				=> 'Đang hoạt động',
			'locked' 			=> 'Đã bị khoá',
			'not_active' 	=> 'Đã ẩn',
			'published' 	=> 'Đang hiển thị',
		];

		return [
			'key' 	=> $status,
			'value' => $arrayStatus[$status] ?? ''
		];
	}
}
