<?php

namespace Modules\App\Models;

class AdHostelPropertiesModel extends DatatableManagerModel
{
	public function getSource()
	{
		return "ad_hostel_properties";
	}

	public function columnMap()
	{
		return array(
			'id' 					=>	'id',
			'group_id'		=>	'group_id',
			'title' 			=>	'title',
			'code'				=>	'code',
			'icon' 				=>	'icon',
			'icon_image' 	=>	'icon_image',
			'is_for_room'	=>	'is_for_room',
			'status' 			=>	'status',
			'position' 		=>	'position'
		);
	}

	public function initialize()
	{
		$this->belongsTo('group_id', 'Modules\App\Models\AdHostelPropertiesGroupModel', 'id', ['alias' => 'group']);
	}

	private function getController()
	{
		return "ad-hostel-properties";
	}

	public function my_url($_format, $id = null)
	{
		$Ohi = new \Modules\Library\Oh\Ohi();
		return $Ohi->baseAdminUrl($this->getController() . "/index/{$_format}/{$id}");
	}

	public function datatable_column()
	{
		$group = new AdHostelPropertiesGroupModel();
		$init_data = array(
			array(
				'name'		=> 'ahp@title',
				'label' 	=> 'Tiêu đề',
				'filter'	=> array(
					'type' 	=> 'text'
				)
			),
			array(
				'name'		=> 'ahp@group_id',
				'label' 	=> 'Thuộc nhóm',
				'filter'	=> array(
					'type' 	=> 'select',
					'value'	=> $group->GetListItem(),
				)
			),
			array(
				'name'		=> 'ahp@status',
				'width'		=> '80px',
				'label' 	=> 'Hiển thị',
				'filter'	=> array(
					'type' 	=> 'select',
					'value' => array(0 => 'Không', 1 => 'Có')
				)
			),
			array(
				'name'		=> 'm@username',
				'label' 	=> 'Người tạo',
				'width'		=> '80px',
				'filter'	=> array(
					'type' 	=> 'text'
				)
			),
			array(
				'name'		=> 'ahp@created',
				'width'		=> '100px',
				'label' 	=> 'Ngày tạo',
				'filter'	=> array(
					'type' 	=> 'text'
				)
			),
			array(
				'name' 		=> 'ahp@id',
				'label'		=> '<a href="' . $this->my_url('add') . '" class="btn btn-primary"><i class="fa fa-plus"></i> Thêm</a>',
				'width'		=> '100px'
			)
		);
		return $init_data;
	}

	public function datatable_json_data()
	{
		$result = $this->datatable_find([
			'select' 		=> ['ahp.title', 'ahp.status', 'm.username', 'ahpg.title AS group_title', 'ahp.created', 'ahp.id'],
			'from' 			=> ['ahp' => 'Modules\App\Models\AdHostelPropertiesModel'],
			'leftJoin' 	=> [
				'Modules\App\Models\ErpMemberModel' => ['alias' => 'm', 'on' => 'ahp.creator_id=m.id'],
				'Modules\App\Models\AdHostelPropertiesGroupModel' => ['alias' => 'ahpg', 'on' => 'ahp.group_id=ahpg.id'],
			],
			'group_by' => array('ht.id')
		]);

		$result->json_data = array();

		foreach ($result->realData as $item) {
			$action = array(
				'edit' 		=> array('href' => $this->my_url('update', $item->id)),
				'delete' 	=> array('href' => $this->my_url('delete', $item->id))
			);

			$result->json_data[] = array(
				'hp@title' 			=> $item->title,
				'hp@group_id' 	=> $item->group_title,
				'hp@status'    	=> $this->changeValue('status', $item->id, $item->status, $this->getController()),
				'm@username' 	  => $item->username,
				'hp@created' 		=> $item->created,
				'hp@id'					=> $this->renderHtmlButton($action)
			);
		}
		return $result;
	}

	public function GetListItem()
	{
		$list = [];
		$list_item =  $this->find(['order' => 'position ASC, title ASC']);
		foreach ($list_item as $value) {
			$list[$value->id]    = $value->title;
		}
		return $list;
	}
}
